# Mark <PERSON>cham Photography Website

A professional photography website featuring Google Photos API integration for dynamic gallery management.

## Features

- **Responsive Design**: Mobile-first design optimized for all devices
- **Google Photos Integration**: Live album embedding and automatic photo syncing
- **Dynamic Galleries**: Wildlife, Sports, and Real Estate photography sections
- **Professional Booking**: Contact forms and service package displays
- **Admin Panel**: Easy album management and Google Photos configuration

## Google Photos API Setup

### 1. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the **Google Photos Library API**:
   - Navigate to "APIs & Services" > "Library"
   - Search for "Photos Library API"
   - Click "Enable"

### 2. Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Configure the OAuth consent screen if prompted
4. Set Application type to "Web application"
5. Add authorized redirect URIs:
   - For development: `http://localhost:5173/`
   - For production: `https://magenta-sherbet-1ac7c6.netlify.app/`

### 3. Environment Configuration

1. Copy `.env.example` to `.env`
2. Add your Google credentials:

```env
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here
VITE_GOOGLE_CLIENT_SECRET=your_google_client_secret_here
VITE_GOOGLE_REDIRECT_URI=http://localhost:5173/auth/callback
```

### 4. Album Organization

For best results, organize your Google Photos into albums:

- **Wildlife Photography**: Create albums with names like "Wildlife", "Nature", "Birds"
- **Sports Photography**: Create albums like "Sports", "Baseball", "Football"
- **Real Estate**: Create albums like "Real Estate", "Properties", "Homes"

### 5. Connect and Configure

1. Start the development server: `npm run dev`
2. Click the admin panel button (gear icon) in the bottom right
3. Connect your Google Photos account
4. Map your albums to the appropriate gallery categories
5. Your live photos will now appear on the website!

## Usage

### Admin Panel

Access the admin panel by clicking the settings icon in the bottom right corner. From here you can:

- Connect/disconnect Google Photos
- View all available albums
- Map albums to gallery categories
- Preview album contents

### Gallery Integration

The website automatically displays photos from your mapped Google Photos albums. When you add new photos to your albums, they'll appear on the website automatically.

### Switching Between Static and Live Photos

Use the "Live Albums" toggle in the portfolio section to switch between:
- Static placeholder photos (for demo purposes)
- Live Google Photos albums (your actual photography)

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## Security Notes

- OAuth tokens are stored in localStorage for persistence
- Tokens are automatically refreshed when expired
- Only read-only access to Google Photos is requested
- No photos are downloaded or stored locally

## Troubleshooting

### Common Issues

1. **"Authentication Error"**: Check your Google Cloud credentials and redirect URIs
2. **"No albums found"**: Ensure you have albums created in Google Photos
3. **"Photos not loading"**: Verify the album contains photos and isn't empty
4. **"Redirect URI mismatch"**: Ensure the redirect URI in Google Cloud matches your .env file

### Support

For technical support with the Google Photos integration, check:
1. Google Cloud Console for API quotas and errors
2. Browser developer console for JavaScript errors
3. Network tab for failed API requests

## Color Scheme

- **Primary**: University of Tennessee Orange (#FF8200)
- **Accent**: Pat Summitt Blue (#A9D0F5)
- **Neutrals**: White, light gray, dark charcoal

## Typography

Clean, modern fonts optimized for readability across all devices.