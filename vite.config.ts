import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  define: {
    // Ensure environment variables are available at build time
    __VITE_GOOGLE_CLIENT_ID__: JSON.stringify(process.env.VITE_GOOGLE_CLIENT_ID),
    __VITE_GOOGLE_CLIENT_SECRET__: JSON.stringify(process.env.VITE_GOOGLE_CLIENT_SECRET),
    __VITE_GOOGLE_REDIRECT_URI__: JSON.stringify(process.env.VITE_GOOGLE_REDIRECT_URI),
  },
}));
