import React from 'react';
import { Camera, Heart, Home, CheckCircle } from 'lucide-react';

const Services: React.FC = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="services" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Services & Packages
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Professional photography services designed to capture life's most precious moments
          </p>
        </div>

        {/* Main Focus: Game Day Package */}
        <div className="bg-gradient-to-br from-tennessee-orange-500 to-tennessee-orange-600 rounded-2xl overflow-hidden mb-12 shadow-2xl">
          <div className="grid lg:grid-cols-2 gap-0">
            {/* Content Side */}
            <div className="p-8 md:p-12 text-white">
              <div className="text-center lg:text-left mb-8">
                <Camera className="h-16 w-16 mx-auto lg:mx-0 mb-4 text-tennessee-orange-100" />
                <h3 className="text-3xl md:text-4xl font-bold mb-4">The Game Day Package</h3>
                <div className="text-5xl md:text-6xl font-bold mb-2">$250</div>
                <p className="text-xl text-tennessee-orange-100">5 High-Resolution Digital Photos</p>
              </div>

              <div className="mb-8">
                <p className="text-lg mb-6 text-center lg:text-left leading-relaxed">
                  Enjoy the game while we focus on capturing your child's key moments. You'll receive a
                  professional gallery of edited images to choose your favorites from.
                </p>
              </div>

              <div className="grid grid-cols-1 gap-3 mb-8">
                {[
                  'Professional-grade equipment',
                  'Focus on your child throughout the game',
                  'High-resolution digital delivery',
                  '5 professionally edited photos',
                  'Quick turnaround time',
                  'Perfect for sharing and printing'
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-tennessee-orange-200 flex-shrink-0" />
                    <span className="text-tennessee-orange-100">{feature}</span>
                  </div>
                ))}
              </div>

              <div className="text-center lg:text-left">
                <button
                  onClick={scrollToContact}
                  className="bg-white text-tennessee-orange-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-tennessee-orange-50 transition-colors duration-200 shadow-lg transform hover:scale-105"
                >
                  Book Your Game Day Package
                </button>
              </div>
            </div>

            {/* Image Side */}
            <div className="relative h-96 lg:h-auto">
              <img
                src="https://storage.googleapis.com/msgsndr/IXfjxQ6NAL6Cng2RQk7i/media/688135962c4791bd4cdc67f7.jpeg"
                alt="Mark Beecham Photography - Youth sports action shot"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-tennessee-orange-500/30 lg:to-tennessee-orange-500/20"></div>
            </div>
          </div>
        </div>

        {/* Other Services */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Wildlife Photography */}
          <div className="bg-gray-50 rounded-xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
            <div className="bg-gray-100 rounded-full p-4 w-20 h-20 mx-auto mb-6 flex items-center justify-center">
              <Camera className="h-10 w-10 text-gray-600" />
            </div>
            <h4 className="text-2xl font-bold text-gray-900 mb-4">Wildlife Photography</h4>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Stunning prints of Tennessee's magnificent wildlife. Perfect for home or office decoration.
            </p>
            <button
              onClick={scrollToContact}
              className="text-gray-600 hover:text-gray-700 font-medium hover:underline"
            >
              View Wildlife Prints →
            </button>
          </div>

          {/* Family Portraits */}
          <div className="bg-gray-50 rounded-xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
            <div className="bg-gray-100 rounded-full p-4 w-20 h-20 mx-auto mb-6 flex items-center justify-center">
              <Heart className="h-10 w-10 text-gray-600" />
            </div>
            <h4 className="text-2xl font-bold text-gray-900 mb-4">Family Portraits</h4>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Beautiful family portrait sessions that capture the love and connection between family members.
            </p>
            <button
              onClick={scrollToContact}
              className="text-gray-600 hover:text-gray-700 font-medium hover:underline"
            >
              Book Family Session →
            </button>
          </div>

          {/* Real Estate */}
          <div className="bg-gray-50 rounded-xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 md:col-span-2 lg:col-span-1">
            <div className="bg-gray-100 rounded-full p-4 w-20 h-20 mx-auto mb-6 flex items-center justify-center">
              <Home className="h-10 w-10 text-gray-600" />
            </div>
            <h4 className="text-2xl font-bold text-gray-900 mb-4">Real Estate</h4>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Professional photography to make your listings shine and attract potential buyers.
            </p>
            <button
              onClick={scrollToContact}
              className="text-gray-600 hover:text-gray-700 font-medium hover:underline"
            >
              Contact for Details →
            </button>
          </div>
        </div>

        {/* Process Section */}
        <div className="mt-20 bg-gray-50 rounded-2xl p-8 md:p-12">
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">How It Works</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-gray-800 text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold mx-auto mb-4">1</div>
              <h4 className="text-xl font-semibold mb-3">Book Your Session</h4>
              <p className="text-gray-600">Contact us to schedule your photography session and discuss your specific needs.</p>
            </div>
            <div className="text-center">
              <div className="bg-gray-800 text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold mx-auto mb-4">2</div>
              <h4 className="text-xl font-semibold mb-3">We Capture the Moments</h4>
              <p className="text-gray-600">Relax and enjoy while we focus on capturing the perfect shots with professional equipment.</p>
            </div>
            <div className="text-center">
              <div className="bg-gray-800 text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold mx-auto mb-4">3</div>
              <h4 className="text-xl font-semibold mb-3">Receive Your Photos</h4>
              <p className="text-gray-600">Get your professionally edited, high-resolution photos delivered digitally within days.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;