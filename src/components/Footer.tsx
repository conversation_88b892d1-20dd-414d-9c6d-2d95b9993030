import React from 'react';
import { Camera, Heart, MapPin, Phone, Mail } from 'lucide-react';

const Footer: React.FC = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-gray-900 text-white py-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <Camera className="h-8 w-8 text-tennessee-orange-500" />
              <h3 className="text-2xl font-bold">Mark <PERSON> Photography</h3>
            </div>
            <p className="text-gray-300 mb-4 leading-relaxed max-w-md">
              "You do life. We'll capture the memory." Professional photography services
              specializing in sports, wildlife, and family portraits throughout Tennessee.
            </p>
            <div className="flex items-center space-x-2 text-tennessee-orange-400">
              <Heart className="h-5 w-5" />
              <span className="text-sm">Capturing God's creation and life's precious moments</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              {[
                { name: 'Home', id: 'hero' },
                { name: 'Portfolio', id: 'portfolio' },
                { name: 'Services', id: 'services' },
                { name: 'About', id: 'about' },
                { name: 'Contact', id: 'contact' }
              ].map((item) => (
                <li key={item.id}>
                  <button
                    onClick={() => scrollToSection(item.id)}
                    className="text-gray-300 hover:text-tennessee-orange-400 transition-colors duration-200"
                  >
                    {item.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-tennessee-orange-500" />
                <span className="text-gray-300">(*************</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-tennessee-orange-500" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-tennessee-orange-500" />
                <span className="text-gray-300">Tennessee & Surrounding Areas</span>
              </div>
            </div>
          </div>
        </div>

        {/* Services */}
        <div className="border-t border-gray-800 pt-8 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center md:text-left">
              <h5 className="font-semibold text-orange-400 mb-2">Sports Photography</h5>
              <p className="text-sm text-gray-300">Game Day Package - $250</p>
            </div>
            <div className="text-center md:text-left">
              <h5 className="font-semibold text-orange-400 mb-2">Wildlife Photography</h5>
              <p className="text-sm text-gray-300">Stunning prints available</p>
            </div>
            <div className="text-center md:text-left">
              <h5 className="font-semibold text-orange-400 mb-2">Family Portraits</h5>
              <p className="text-sm text-gray-300">Custom session pricing</p>
            </div>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-gray-800 pt-8 text-center">
          <p className="text-gray-400 mb-4">
            &copy; 2024 Mark Beecham Photography. All rights reserved.
          </p>
          <p className="text-sm text-gray-500 italic">
            "They speak of the glorious splendor of your majesty—and I will meditate on your wonderful works." - Psalm 145:5
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;