import React from 'react';
import { ArrowRight } from 'lucide-react';

const Hero: React.FC = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(https://storage.googleapis.com/msgsndr/IXfjxQ6NAL6Cng2RQk7i/media/688114d1744d7ee720d54d1a.webp)'
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-3xl mx-auto">
        <div className="mb-8">
          <p className="text-xl md:text-2xl mb-6 font-light tracking-wide opacity-90">
            "You do life. We'll capture the memory."
          </p>
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            Mark Beecham Photography
          </h1>
        </div>

        <p className="text-lg md:text-xl mb-12 max-w-xl mx-auto leading-relaxed font-light opacity-90">
          Preserving life's precious moments in Tennessee
        </p>

        <button
          onClick={scrollToContact}
          className="bg-tennessee-orange-500 text-white px-10 py-4 rounded-full hover:bg-tennessee-orange-600 transition-all duration-300 font-medium text-lg flex items-center space-x-2 transform hover:scale-105 mx-auto"
        >
          <span>Book Your Session</span>
          <ArrowRight size={20} />
        </button>
      </div>
    </section>
  );
};

export default Hero;