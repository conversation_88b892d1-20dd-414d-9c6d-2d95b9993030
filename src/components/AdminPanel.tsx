import React, { useState, useEffect } from 'react';
import { <PERSON>tings, Camera, RefreshCw, ExternalLink, Trash2, Eye } from 'lucide-react';
import GooglePhotosAuth from './GooglePhotosAuth';
import { googlePhotosService, GoogleAlbum, GooglePhoto } from '../services/googlePhotos';

const AdminPanel: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [albums, setAlbums] = useState<GoogleAlbum[]>([]);
  const [selectedAlbum, setSelectedAlbum] = useState<string>('');
  const [albumPhotos, setAlbumPhotos] = useState<GooglePhoto[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [albumMappings, setAlbumMappings] = useState({
    wildlife: '',
    sports: '',
    realestate: ''
  });

  useEffect(() => {
    // Load saved album mappings
    const saved = localStorage.getItem('album_mappings');
    if (saved) {
      setAlbumMappings(JSON.parse(saved));
    }
  }, []);

  const loadAlbums = async () => {
    setIsLoading(true);
    try {
      const albumList = await googlePhotosService.getAlbums();
      setAlbums(albumList);
    } catch (error) {
      console.error('Error loading albums:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadAlbumPhotos = async (albumId: string) => {
    if (!albumId) return;
    
    setIsLoading(true);
    try {
      const photos = await googlePhotosService.getAlbumPhotos(albumId, 20);
      setAlbumPhotos(photos);
    } catch (error) {
      console.error('Error loading album photos:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveAlbumMapping = (category: keyof typeof albumMappings, albumId: string) => {
    const newMappings = { ...albumMappings, [category]: albumId };
    setAlbumMappings(newMappings);
    localStorage.setItem('album_mappings', JSON.stringify(newMappings));
  };

  const clearMapping = (category: keyof typeof albumMappings) => {
    const newMappings = { ...albumMappings, [category]: '' };
    setAlbumMappings(newMappings);
    localStorage.setItem('album_mappings', JSON.stringify(newMappings));
  };

  const getAlbumName = (albumId: string) => {
    const album = albums.find(a => a.id === albumId);
    return album ? album.title : 'Unknown Album';
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition-colors duration-200 z-40"
        title="Admin Panel"
      >
        <Settings className="h-6 w-6" />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">Admin Panel</h2>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-6 space-y-8">
          {/* Google Photos Authentication */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Google Photos Connection</h3>
            
            {/* Debug Info */}
            <div className="bg-gray-100 rounded-lg p-4 mb-4 text-sm">
              <h4 className="font-medium mb-2">Debug Information:</h4>
              <div className="space-y-1 font-mono text-xs">
                <div>Client ID: {import.meta.env.VITE_GOOGLE_CLIENT_ID ? 'Configured' : 'Missing'}</div>
                <div>Client Secret: {import.meta.env.VITE_GOOGLE_CLIENT_SECRET ? 'Configured' : 'Missing'}</div>
                <div>Redirect URI: {import.meta.env.VITE_GOOGLE_REDIRECT_URI || 'Missing'}</div>
                <div>Environment: {import.meta.env.MODE}</div>
              </div>
            </div>
            
            <GooglePhotosAuth onAuthSuccess={loadAlbums} />
          </div>

          {/* Album Management */}
          {googlePhotosService.isAuthenticated() && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Album Management</h3>
                <button
                  onClick={loadAlbums}
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
                >
                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                  <span>Refresh Albums</span>
                </button>
              </div>

              {/* Album Mappings */}
              <div className="space-y-4 mb-6">
                {Object.entries(albumMappings).map(([category, albumId]) => (
                  <div key={category} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 capitalize">{category} Gallery</h4>
                      {albumId && (
                        <button
                          onClick={() => clearMapping(category as keyof typeof albumMappings)}
                          className="text-red-600 hover:text-red-700"
                          title="Clear mapping"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                    
                    <select
                      value={albumId}
                      onChange={(e) => saveAlbumMapping(category as keyof typeof albumMappings, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    >
                      <option value="">Select album for {category}...</option>
                      {albums.map((album) => (
                        <option key={album.id} value={album.id}>
                          {album.title} ({album.mediaItemsCount} photos)
                        </option>
                      ))}
                    </select>
                    
                    {albumId && (
                      <p className="text-sm text-gray-600 mt-2">
                        Mapped to: {getAlbumName(albumId)}
                      </p>
                    )}
                  </div>
                ))}
              </div>

              {/* Album Preview */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Album Preview</h4>
                <select
                  value={selectedAlbum}
                  onChange={(e) => {
                    setSelectedAlbum(e.target.value);
                    loadAlbumPhotos(e.target.value);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent mb-4"
                >
                  <option value="">Select album to preview...</option>
                  {albums.map((album) => (
                    <option key={album.id} value={album.id}>
                      {album.title} ({album.mediaItemsCount} photos)
                    </option>
                  ))}
                </select>

                {albumPhotos.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {albumPhotos.slice(0, 8).map((photo) => (
                      <div key={photo.id} className="aspect-square overflow-hidden rounded-lg">
                        <img
                          src={googlePhotosService.getPhotoUrl(photo.baseUrl, 300, 300)}
                          alt={photo.filename}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">Setup Instructions</h3>
            <ol className="list-decimal list-inside space-y-2 text-blue-800">
              <li>Create a Google Cloud Console project</li>
              <li>Enable the Google Photos Library API</li>
              <li>Create OAuth 2.0 credentials</li>
              <li>Add your credentials to the .env file</li>
              <li>Connect your Google Photos account above</li>
              <li>Map your albums to the appropriate gallery categories</li>
            </ol>
            <div className="mt-4">
              <a
                href="https://console.cloud.google.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-700"
              >
                <ExternalLink className="h-4 w-4" />
                <span>Open Google Cloud Console</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;