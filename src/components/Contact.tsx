import React from 'react';
import { Phone, Mail, MapPin, Send } from 'lucide-react';

const Contact: React.FC = () => {

  return (
    <section id="contact" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Contact & Booking
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Ready to capture your memories? Get in touch to schedule your session.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Contact Form */}
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Send a Message</h3>
            
            <form name="contact" method="POST" data-netlify="true" action="/thank-you.html" className="space-y-6">
              <input type="hidden" name="form-name" value="contact" />
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tennessee-orange-500 focus:border-transparent transition-all duration-200"
                  placeholder="Your full name"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tennessee-orange-500 focus:border-transparent transition-all duration-200"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tennessee-orange-500 focus:border-transparent transition-all duration-200"
                  placeholder="(*************"
                />
              </div>

              <div>
                <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Interested In *
                </label>
                <select
                  id="service"
                  name="service"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tennessee-orange-500 focus:border-transparent transition-all duration-200"
                >
                  <option value="">Select a service</option>
                  <option value="sports">Sports Photography - Game Day Package ($250)</option>
                  <option value="family">Family Portrait Session</option>
                  <option value="wildlife">Wildlife Photography/Prints</option>
                  <option value="realestate">Real Estate Photography</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tennessee-orange-500 focus:border-transparent transition-all duration-200"
                  placeholder="Tell us about your event, preferred dates, or any special requests..."
                ></textarea>
              </div>

              <button
                type="submit"
                className="w-full bg-tennessee-orange-500 text-white px-8 py-4 rounded-lg hover:bg-tennessee-orange-600 transition-colors duration-200 font-medium text-lg flex items-center justify-center space-x-2 transform hover:scale-105"
              >
                <Send size={20} />
                <span>Send Message</span>
              </button>
            </form>
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Get In Touch</h3>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                Ready to capture your special moments? Contact Mark directly for quick responses 
                and personalized service.
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="bg-gray-100 rounded-full p-3">
                  <Phone className="h-6 w-6 text-gray-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Phone & Text</h4>
                  <p className="text-gray-600">(*************</p>
                  <p className="text-sm text-gray-500">Text preferred for quick responses</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-gray-100 rounded-full p-3">
                  <Mail className="h-6 w-6 text-gray-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Email</h4>
                  <p className="text-gray-600"><EMAIL></p>
                  <p className="text-sm text-gray-500">For detailed inquiries</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-gray-100 rounded-full p-3">
                  <MapPin className="h-6 w-6 text-gray-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Service Area</h4>
                  <p className="text-gray-600">Tennessee & Surrounding Areas</p>
                  <p className="text-sm text-gray-500">Travel available for special events</p>
                </div>
              </div>
            </div>

            {/* Response Time */}
            <div className="bg-gray-50 rounded-xl p-6 border-l-4 border-gray-300">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Quick Response Promise</h4>
              <p className="text-gray-700">
                We typically respond to all inquiries within 24 hours. For urgent requests,
                text or call for the fastest response.
              </p>
            </div>

            {/* Popular Service */}
            <div className="bg-gray-50 rounded-xl p-6 border-l-4 border-gray-300">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Most Popular</h4>
              <p className="text-gray-700 mb-2">Game Day Sports Package - $250</p>
              <p className="text-sm text-gray-600">
                Perfect for parents who want to enjoy the game while we capture the action!
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;