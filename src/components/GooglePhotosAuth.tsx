import React, { useState, useEffect } from 'react';
import { Camera, ExternalLink, CheckCircle, AlertCircle } from 'lucide-react';
import { googlePhotosService } from '../services/googlePhotos';

interface GooglePhotosAuthProps {
  onAuthSuccess?: () => void;
}

const GooglePhotosAuth: React.FC<GooglePhotosAuthProps> = ({ onAuthSuccess }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if already authenticated
    const hasTokens = googlePhotosService.loadStoredTokens();
    console.log('Initial auth check:', { hasTokens });
    setIsAuthenticated(hasTokens);

    // Handle OAuth callback - check for code in URL
    const handleCallback = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      
      console.log('Checking for OAuth callback:', { 
        hasCode: !!code, 
        currentPath: window.location.pathname,
        search: window.location.search 
      });
      
      if (code && !hasTokens) {
        console.log('Processing OAuth callback...');
        await handleAuthCallback(code);
      }
    };
    
    handleCallback();
  }, []);

  const handleAuthCallback = async (code: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await googlePhotosService.exchangeCodeForTokens(code);
      setIsAuthenticated(true);
      onAuthSuccess?.();
      
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } catch (err) {
      setError('Failed to authenticate with Google Photos');
      console.error('Auth error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnect = () => {
    try {
      const authUrl = googlePhotosService.getAuthUrl();
      console.log('Redirecting to auth URL:', authUrl);
      window.location.href = authUrl;
    } catch (err) {
      setError('Failed to initialize Google authentication. Please check configuration.');
      console.error('Auth initialization error:', err);
    }
  };

  const handleDisconnect = () => {
    googlePhotosService.clearAuth();
    setIsAuthenticated(false);
  };

  if (isLoading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="text-blue-800">Connecting to Google Photos...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <AlertCircle className="h-6 w-6 text-red-600" />
          <span className="text-red-800 font-medium">Authentication Error</span>
        </div>
        <p className="text-red-700 mb-4">{error}</p>
        <button
          onClick={handleConnect}
          className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (isAuthenticated) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="h-6 w-6 text-green-600" />
            <div>
              <span className="text-green-800 font-medium">Connected to Google Photos</span>
              <p className="text-green-700 text-sm">Your albums are now accessible</p>
            </div>
          </div>
          <button
            onClick={handleDisconnect}
            className="text-green-700 hover:text-green-800 text-sm underline"
          >
            Disconnect
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-tennessee-orange-50 border border-tennessee-orange-200 rounded-lg p-6">
      <div className="text-center">
        <Camera className="h-12 w-12 text-tennessee-orange-600 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-tennessee-orange-900 mb-2">
          Connect Google Photos
        </h3>
        <p className="text-tennessee-orange-800 mb-6">
          Connect your Google Photos account to automatically display your photography albums on the website.
        </p>
        <button
          onClick={handleConnect}
          className="bg-tennessee-orange-600 text-white px-6 py-3 rounded-lg hover:bg-tennessee-orange-700 transition-colors duration-200 flex items-center space-x-2 mx-auto"
        >
          <ExternalLink className="h-5 w-5" />
          <span>Connect Google Photos</span>
        </button>
        <p className="text-tennessee-orange-700 text-xs mt-3">
          This will redirect you to Google to authorize access to your photos
        </p>
      </div>
    </div>
  );
};

export default GooglePhotosAuth;