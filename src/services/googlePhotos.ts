import { GoogleAuth } from 'google-auth-library';

export interface GooglePhoto {
  id: string;
  baseUrl: string;
  filename: string;
  mimeType: string;
  mediaMetadata: {
    width: string;
    height: string;
    creationTime: string;
  };
}

export interface GoogleAlbum {
  id: string;
  title: string;
  productUrl: string;
  coverPhotoBaseUrl?: string;
  mediaItemsCount: string;
}

class GooglePhotosService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private clientId: string;
  private clientSecret: string;
  private redirectUri: string;

  constructor() {
    // Try environment variables first, then fallback to hardcoded values for production
    this.clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID || '288681907750-14dfp49a47aqmulhkclq4crtcuql2uq0.apps.googleusercontent.com';
    this.clientSecret = import.meta.env.VITE_GOOGLE_CLIENT_SECRET || '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';
    this.redirectUri = import.meta.env.VITE_GOOGLE_REDIRECT_URI || 'https://magenta-sherbet-1ac7c6.netlify.app/auth/callback';
    
    // Debug logging to help identify the issue
    console.log('Environment variables loaded:', {
      clientId: this.clientId ? `Present (${this.clientId.substring(0, 20)}...)` : 'Missing',
      clientSecret: this.clientSecret ? 'Present' : 'Missing',
      redirectUri: this.redirectUri || 'Missing',
      env: import.meta.env.MODE
    });
    
    // Validate credentials
    this.validateCredentials();
  }

  private validateCredentials(): boolean {
    const isValid = !!(this.clientId && this.clientSecret && this.redirectUri);
    if (!isValid) {
      console.error('Missing required Google API credentials:', {
        clientId: !!this.clientId,
        clientSecret: !!this.clientSecret,
        redirectUri: !!this.redirectUri
      });
    }
    return isValid;
  }

  // Initialize OAuth flow
  getAuthUrl(): string {
    const scopes = ['https://www.googleapis.com/auth/photoslibrary.readonly'];
    const params = new URLSearchParams({
      client_id: this.clientId,
      redirect_uri: this.redirectUri,
      response_type: 'code',
      scope: scopes.join(' '),
      access_type: 'offline',
      prompt: 'consent'
    });

    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
  }

  // Handle OAuth callback
  async handleOAuthCallback(): Promise<boolean> {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const error = urlParams.get('error');
    
    console.log('OAuth callback detected:', { code: !!code, error, fullUrl: window.location.href });
    
    if (error) {
      console.error('OAuth error:', error);
      return false;
    }
    
    if (code) {
      try {
        console.log('Exchanging code for tokens...');
        await this.exchangeCodeForTokens(code);
        console.log('Token exchange successful');
        return true;
      } catch (err) {
        console.error('Token exchange failed:', err);
        return false;
      }
    }
    
    return false;
  }

  // Exchange authorization code for tokens
  async exchangeCodeForTokens(code: string): Promise<void> {
    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          code,
          grant_type: 'authorization_code',
          redirect_uri: this.redirectUri,
        }),
      });

      const data = await response.json();
      
      if (data.access_token) {
        this.accessToken = data.access_token;
        this.refreshToken = data.refresh_token;
        
        // Store tokens in localStorage for persistence
        localStorage.setItem('google_access_token', this.accessToken);
        if (this.refreshToken) {
          localStorage.setItem('google_refresh_token', this.refreshToken);
        }
      }
    } catch (error) {
      console.error('Error exchanging code for tokens:', error);
      throw error;
    }
  }

  // Load tokens from localStorage
  loadStoredTokens(): boolean {
    this.accessToken = localStorage.getItem('google_access_token');
    this.refreshToken = localStorage.getItem('google_refresh_token');
    return !!this.accessToken;
  }

  // Refresh access token
  async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          refresh_token: this.refreshToken,
          grant_type: 'refresh_token',
        }),
      });

      const data = await response.json();
      
      if (data.access_token) {
        this.accessToken = data.access_token;
        localStorage.setItem('google_access_token', this.accessToken);
      }
    } catch (error) {
      console.error('Error refreshing access token:', error);
      throw error;
    }
  }

  // Make authenticated API request
  private async makeApiRequest(url: string, options: RequestInit = {}): Promise<any> {
    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (response.status === 401) {
      // Token expired, try to refresh
      await this.refreshAccessToken();
      return this.makeApiRequest(url, options);
    }

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  }

  // Get all albums
  async getAlbums(): Promise<GoogleAlbum[]> {
    try {
      const data = await this.makeApiRequest(
        'https://photoslibrary.googleapis.com/v1/albums'
      );
      return data.albums || [];
    } catch (error) {
      console.error('Error fetching albums:', error);
      return [];
    }
  }

  // Get photos from a specific album
  async getAlbumPhotos(albumId: string, pageSize: number = 50): Promise<GooglePhoto[]> {
    try {
      const data = await this.makeApiRequest(
        'https://photoslibrary.googleapis.com/v1/mediaItems:search',
        {
          method: 'POST',
          body: JSON.stringify({
            albumId,
            pageSize,
          }),
        }
      );
      return data.mediaItems || [];
    } catch (error) {
      console.error('Error fetching album photos:', error);
      return [];
    }
  }

  // Get photo URL with specific dimensions
  getPhotoUrl(baseUrl: string, width: number = 800, height: number = 600): string {
    return `${baseUrl}=w${width}-h${height}-c`;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  // Clear authentication
  clearAuth(): void {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('google_access_token');
    localStorage.removeItem('google_refresh_token');
  }
}

export const googlePhotosService = new GooglePhotosService();